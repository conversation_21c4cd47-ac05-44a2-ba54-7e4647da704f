# Compass Properties Website

A beautiful, modern, responsive website for Compass Properties, a construction and real estate company in Ethiopia.

## Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Hero Slider**: Full-width image carousel with compelling headlines
- **Interactive Elements**: Hover effects, smooth scrolling, and dynamic content
- **PWA Ready**: Progressive Web App capabilities with offline support
- **SEO Optimized**: Proper meta tags and semantic HTML structure
- **Accessibility**: WCAG compliant with proper focus management

## Color Scheme

- **Primary**: Timber Green (#1A3028)
- **Secondary**: Tahiti Gold (#EA8224)
- **Accent**: Mantle (#8C9C94)

## File Structure

```
compass-et-static/
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
├── manifest.json       # PWA manifest
├── sw.js              # Service worker for offline support
├── README.md          # This file
├── data/
│   └── projects.json  # Project data
└── imgs/              # Image assets
    ├── slides/        # Hero slider images
    ├── projects/      # Project images
    ├── properties/    # Property images
    ├── client-logos/  # Client logo images
    └── ...           # Other image folders
```

## Sections

1. **Navigation Bar**: Fixed header with logo and menu items
2. **Hero Slider**: Full-width image carousel with call-to-action buttons
3. **Company Overview**: Mission, vision, and key statistics
4. **Featured Projects**: Showcase of completed construction projects
5. **Available Properties**: Current real estate listings
6. **Call-to-Action Banner**: Encouraging visitor engagement
7. **News & Updates**: Latest company news and project updates
8. **Client Testimonials**: Customer reviews and feedback
9. **Footer**: Contact information, social links, and contact form

## Technologies Used

- **HTML5**: Semantic markup for better SEO and accessibility
- **CSS3**: Modern styling with custom properties and animations
- **Tailwind CSS**: Utility-first CSS framework for rapid development
- **JavaScript (ES6+)**: Modern JavaScript for interactive functionality
- **Font Awesome**: Icon library for consistent iconography
- **Google Fonts**: Inter font family for typography

## Key Features

### Hero Slider
- Auto-advancing slides every 6 seconds
- Manual navigation with arrows and dots
- Keyboard navigation support (arrow keys)
- Pause on hover functionality
- Smooth fade transitions

### Responsive Design
- Mobile-first approach
- Breakpoints for tablet and desktop
- Flexible grid layouts
- Optimized images for different screen sizes

### Interactive Elements
- Smooth scrolling navigation
- Hover effects on cards and buttons
- Mobile menu with hamburger toggle
- Form validation and submission handling
- Scroll-based animations

### Performance Optimizations
- Lazy loading for images
- Debounced and throttled event handlers
- Optimized CSS and JavaScript
- Service worker for caching
- Compressed and optimized assets

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Installation & Setup

1. **Clone or download** the project files
2. **Ensure all files** are in the same directory structure
3. **Open index.html** in a web browser or serve through a web server
4. **For development**: Use a local server like Live Server (VS Code extension) or Python's built-in server

### Using Python Server
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

Then open `http://localhost:8000` in your browser.

### Using Node.js Server
```bash
# Install http-server globally
npm install -g http-server

# Run server
http-server

# Or use npx
npx http-server
```

## Customization

### Colors
Update the color scheme in both `styles.css` and the Tailwind config in `index.html`:

```css
:root {
    --timber-green: #1A3028;
    --tahiti-gold: #EA8224;
    --mantle: #8C9C94;
}
```

### Content
- Update project data in `data/projects.json`
- Replace images in the `imgs/` folder
- Modify text content directly in `index.html`
- Update contact information in the footer

### Styling
- Custom styles are in `styles.css`
- Tailwind utilities are used throughout the HTML
- Responsive breakpoints can be adjusted in the CSS

## Image Requirements

### Hero Slider Images
- **Dimensions**: 1920x1080px (16:9 aspect ratio)
- **Format**: JPG or WebP
- **Size**: Under 500KB each for optimal loading

### Project/Property Images
- **Dimensions**: 800x600px (4:3 aspect ratio)
- **Format**: JPG, PNG, or WebP
- **Size**: Under 200KB each

### Logo
- **Format**: PNG with transparent background
- **Dimensions**: 200x80px (scalable)

## SEO Optimization

The website includes:
- Semantic HTML5 structure
- Meta tags for description and keywords
- Open Graph tags for social media
- Twitter Card tags
- Structured data markup
- Alt text for all images
- Proper heading hierarchy

## Accessibility Features

- ARIA labels and roles
- Keyboard navigation support
- Focus management
- High contrast ratios
- Screen reader compatibility
- Semantic HTML elements

## Performance

- Optimized images with proper sizing
- Minified CSS and JavaScript (for production)
- Lazy loading for below-the-fold content
- Service worker for caching
- Efficient event handling

## Future Enhancements

- Content Management System integration
- Multi-language support (Amharic/English)
- Advanced property search and filtering
- Virtual tour integration
- Online booking system
- Blog/news management system
- Customer portal
- Payment gateway integration

## Support

For technical support or customization requests, please contact the development team.

## License

This website is proprietary to Compass Properties. All rights reserved.
