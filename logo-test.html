<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo and Favicon Test - Compass Properties</title>
    <link rel="icon" type="image/x-icon" href="imgs/favicon.ico">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .logo-container {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .dark-bg {
            background: #1A3028;
            color: white;
        }
        .logo {
            max-height: 60px;
            width: auto;
        }
        .logo-inverted {
            filter: brightness(0) invert(1);
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🎯 Logo and Favicon Test - Compass Properties</h1>
    
    <div class="test-section">
        <h2>📋 Test Overview</h2>
        <p>This page tests the logo and favicon implementation for the Compass Properties website.</p>
        <p><strong>Files being tested:</strong></p>
        <ul>
            <li><code>imgs/balck-white-logo.png</code> - Main logo file</li>
            <li><code>imgs/favicon.ico</code> - Favicon file</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🖼️ Logo Display Tests</h2>
        
        <h3>Navigation Bar Logo (Light Background)</h3>
        <div class="logo-container">
            <img src="imgs/balck-white-logo.png" alt="Compass Properties Logo" class="logo" id="navbar-logo">
            <div>
                <strong>Navbar Logo</strong><br>
                <span id="navbar-status" class="status info">Loading...</span><br>
                <small>Should display clearly on white background</small>
            </div>
        </div>

        <h3>Footer Logo (Dark Background)</h3>
        <div class="logo-container dark-bg">
            <img src="imgs/balck-white-logo.png" alt="Compass Properties Logo" class="logo logo-inverted" id="footer-logo">
            <div>
                <strong>Footer Logo</strong><br>
                <span id="footer-status" class="status info">Loading...</span><br>
                <small>Should display as white/inverted on dark background</small>
            </div>
        </div>

        <h3>Logo Variations</h3>
        <div class="logo-container">
            <div style="display: flex; flex-direction: column; gap: 10px; align-items: center;">
                <img src="imgs/balck-white-logo.png" alt="Small Logo" style="height: 30px;">
                <span>Small (30px)</span>
            </div>
            <div style="display: flex; flex-direction: column; gap: 10px; align-items: center;">
                <img src="imgs/balck-white-logo.png" alt="Medium Logo" style="height: 50px;">
                <span>Medium (50px)</span>
            </div>
            <div style="display: flex; flex-direction: column; gap: 10px; align-items: center;">
                <img src="imgs/balck-white-logo.png" alt="Large Logo" style="height: 80px;">
                <span>Large (80px)</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔖 Favicon Test</h2>
        <p>Check the browser tab to see if the favicon is displaying correctly.</p>
        <div class="logo-container">
            <img src="imgs/favicon.ico" alt="Favicon" style="width: 32px; height: 32px; image-rendering: pixelated;">
            <div>
                <strong>Favicon Preview</strong><br>
                <span id="favicon-status" class="status info">Loading...</span><br>
                <small>Should appear in browser tab and bookmarks</small>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Checklist</h2>
        <div id="checklist">
            <p><strong>Manual checks to perform:</strong></p>
            <ul>
                <li>✓ Favicon appears in browser tab</li>
                <li>✓ Logo displays clearly on light background</li>
                <li>✓ Logo displays clearly on dark background (inverted)</li>
                <li>✓ Logo scales properly at different sizes</li>
                <li>✓ No broken image icons</li>
                <li>✓ Images load quickly</li>
                <li>✓ Alt text is descriptive</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Navigation</h2>
        <p>
            <a href="index.html" style="background: #EA8224; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                ← Back to Main Website
            </a>
        </p>
    </div>

    <script>
        // Test if images load successfully
        function testImageLoad(imgId, statusId) {
            const img = document.getElementById(imgId);
            const status = document.getElementById(statusId);
            
            img.onload = function() {
                status.textContent = 'Loaded Successfully';
                status.className = 'status success';
            };
            
            img.onerror = function() {
                status.textContent = 'Failed to Load';
                status.className = 'status error';
            };
            
            // Force reload to trigger events
            img.src = img.src;
        }

        // Test favicon
        function testFavicon() {
            const status = document.getElementById('favicon-status');
            const link = document.querySelector('link[rel="icon"]');
            
            if (link && link.href) {
                // Create a test image to check if favicon loads
                const testImg = new Image();
                testImg.onload = function() {
                    status.textContent = 'Favicon Available';
                    status.className = 'status success';
                };
                testImg.onerror = function() {
                    status.textContent = 'Favicon Not Found';
                    status.className = 'status error';
                };
                testImg.src = link.href;
            } else {
                status.textContent = 'No Favicon Link Found';
                status.className = 'status error';
            }
        }

        // Run tests when page loads
        window.addEventListener('load', function() {
            testImageLoad('navbar-logo', 'navbar-status');
            testImageLoad('footer-logo', 'footer-status');
            testFavicon();
        });

        // Add some interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to logos
            const logos = document.querySelectorAll('.logo');
            logos.forEach(logo => {
                logo.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                logo.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
