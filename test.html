<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compass Properties - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        h1 { color: #1A3028; }
        h2 { color: #EA8224; }
        .checklist { list-style-type: none; }
        .checklist li { margin: 5px 0; }
        .checklist li:before { content: "✓ "; color: green; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Compass Properties Website - Testing Checklist</h1>
    
    <div class="test-section info">
        <h2>Website Overview</h2>
        <p>This is a comprehensive testing page for the Compass Properties website. Use this checklist to verify all features are working correctly across different devices and browsers.</p>
    </div>

    <div class="test-section">
        <h2>File Structure Verification</h2>
        <ul class="checklist">
            <li>index.html - Main website file</li>
            <li>styles.css - Custom styling</li>
            <li>script.js - JavaScript functionality</li>
            <li>manifest.json - PWA manifest</li>
            <li>sw.js - Service worker</li>
            <li>README.md - Documentation</li>
            <li>imgs/ folder with all required images</li>
            <li>data/projects.json - Project data</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Responsive Design Testing</h2>
        <p><strong>Test the website at these breakpoints:</strong></p>
        <ul class="checklist">
            <li>Mobile: 320px - 768px</li>
            <li>Tablet: 768px - 1024px</li>
            <li>Desktop: 1024px+</li>
            <li>Large Desktop: 1440px+</li>
        </ul>
        
        <p><strong>What to check:</strong></p>
        <ul class="checklist">
            <li>Navigation menu collapses to hamburger on mobile</li>
            <li>Hero slider images scale properly</li>
            <li>Text remains readable at all sizes</li>
            <li>Cards stack properly on smaller screens</li>
            <li>Contact form is usable on mobile</li>
            <li>Footer content reorganizes appropriately</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Navigation Testing</h2>
        <ul class="checklist">
            <li>All navigation links work and scroll to correct sections</li>
            <li>Mobile menu opens and closes properly</li>
            <li>Navbar background changes on scroll</li>
            <li>Smooth scrolling is enabled</li>
            <li>Logo is visible and properly sized</li>
            <li>Active states work on hover</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Hero Slider Testing</h2>
        <ul class="checklist">
            <li>All three slides display correctly</li>
            <li>Auto-advance works (6-second intervals)</li>
            <li>Manual navigation arrows work</li>
            <li>Dot navigation works</li>
            <li>Keyboard navigation (arrow keys) works</li>
            <li>Slider pauses on hover</li>
            <li>Images load properly and are optimized</li>
            <li>Text overlays are readable</li>
            <li>Call-to-action buttons work</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Content Sections Testing</h2>
        <ul class="checklist">
            <li>Company Overview section displays mission/vision</li>
            <li>Featured Projects show correct project information</li>
            <li>Available Properties display property details</li>
            <li>Call-to-Action banner is prominent and functional</li>
            <li>News & Updates section shows latest articles</li>
            <li>Client Testimonials display properly</li>
            <li>All images load correctly</li>
            <li>Hover effects work on cards</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Footer and Contact Testing</h2>
        <ul class="checklist">
            <li>Contact information is accurate and clickable</li>
            <li>Social media links are present (placeholder)</li>
            <li>Contact form accepts input</li>
            <li>Form validation works</li>
            <li>Form submission shows success message</li>
            <li>Client logos display properly</li>
            <li>Copyright information is current</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Performance Testing</h2>
        <ul class="checklist">
            <li>Page loads quickly (under 3 seconds)</li>
            <li>Images are optimized and load efficiently</li>
            <li>No JavaScript errors in console</li>
            <li>Smooth animations and transitions</li>
            <li>Service worker registers successfully</li>
            <li>Website works offline (basic functionality)</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Browser Compatibility</h2>
        <p><strong>Test in these browsers:</strong></p>
        <ul class="checklist">
            <li>Chrome (latest)</li>
            <li>Firefox (latest)</li>
            <li>Safari (latest)</li>
            <li>Edge (latest)</li>
            <li>Mobile Safari (iOS)</li>
            <li>Chrome Mobile (Android)</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Accessibility Testing</h2>
        <ul class="checklist">
            <li>All images have alt text</li>
            <li>Proper heading hierarchy (H1, H2, H3)</li>
            <li>Keyboard navigation works</li>
            <li>Focus indicators are visible</li>
            <li>Color contrast meets WCAG standards</li>
            <li>Screen reader compatibility</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>SEO and Meta Tags</h2>
        <ul class="checklist">
            <li>Title tag is descriptive and under 60 characters</li>
            <li>Meta description is compelling and under 160 characters</li>
            <li>Open Graph tags for social sharing</li>
            <li>Twitter Card tags</li>
            <li>Favicon displays correctly</li>
            <li>Structured data markup (if applicable)</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>Testing Instructions</h2>
        <ol>
            <li><strong>Open the main website:</strong> <a href="index.html" target="_blank">index.html</a></li>
            <li><strong>Test on different devices:</strong> Use browser dev tools to simulate mobile/tablet</li>
            <li><strong>Check all functionality:</strong> Go through each section of the checklist</li>
            <li><strong>Test forms:</strong> Try submitting the contact form</li>
            <li><strong>Check console:</strong> Look for any JavaScript errors</li>
            <li><strong>Test offline:</strong> Disconnect internet and reload page</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Color Scheme Reference</h2>
        <div style="display: flex; gap: 20px; margin: 10px 0;">
            <div style="background: #1A3028; color: white; padding: 10px; border-radius: 5px;">
                Timber Green<br>#1A3028
            </div>
            <div style="background: #EA8224; color: white; padding: 10px; border-radius: 5px;">
                Tahiti Gold<br>#EA8224
            </div>
            <div style="background: #8C9C94; color: white; padding: 10px; border-radius: 5px;">
                Mantle<br>#8C9C94
            </div>
        </div>
    </div>

    <div class="test-section pass">
        <h2>✅ Testing Complete</h2>
        <p>Once you've verified all items in this checklist, the Compass Properties website is ready for deployment!</p>
    </div>

    <script>
        // Simple script to check if main website files exist
        function checkFileExists(url) {
            return fetch(url, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }

        // Check main files
        const filesToCheck = ['index.html', 'styles.css', 'script.js', 'manifest.json'];
        
        filesToCheck.forEach(file => {
            checkFileExists(file).then(exists => {
                if (!exists) {
                    console.warn(`File not found: ${file}`);
                }
            });
        });
    </script>
</body>
</html>
