/* Custom CSS for Compass Properties Website */

/* Root Variables */
:root {
    --timber-green: #1A3028;
    --tahiti-gold: #EA8224;
    --mantle: #8C9C94;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-600: #4b5563;
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--timber-green);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

/* Hero Slider Enhancements */
.hero-slider {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Navigation Enhancements */
.navbar-scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}

/* Button Hover Effects */
.btn-primary {
    background-color: var(--tahiti-gold);
    color: white;
    padding: 12px 32px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #d67419;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(234, 130, 36, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: var(--timber-green);
    padding: 12px 32px;
    border: 2px solid var(--timber-green);
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-secondary:hover {
    background-color: var(--timber-green);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(26, 48, 40, 0.3);
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card img {
    transition: transform 0.3s ease;
}

.card:hover img {
    transform: scale(1.05);
}

/* Project and Property Cards */
.project-card, .property-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.project-card:hover, .property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Testimonial Cards */
.testimonial-card {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 60px;
    color: var(--tahiti-gold);
    font-family: serif;
    opacity: 0.3;
}

/* Form Styling */
.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--tahiti-gold);
    box-shadow: 0 0 0 3px rgba(234, 130, 36, 0.1);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .hero-slider h1 {
        font-size: 2.5rem;
    }
    
    .hero-slider p {
        font-size: 1.1rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .btn-primary, .btn-secondary {
        padding: 10px 24px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .hero-slider h1 {
        font-size: 2rem;
    }
    
    .hero-slider p {
        font-size: 1rem;
    }
    
    .testimonial-card {
        padding: 20px;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--timber-green), var(--tahiti-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--timber-green), var(--mantle));
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(26, 48, 40, 0.15);
}

/* Scroll Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-animate.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--mantle);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--timber-green);
}

/* Image Optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
a:focus, button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid var(--tahiti-gold);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .no-print {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
}
